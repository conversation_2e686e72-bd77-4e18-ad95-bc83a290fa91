@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --muted-foreground: #525252; /* zinc-600 equivalent but darker for better contrast */
  --muted-foreground-light: #737373; /* zinc-500 for less important text */
}

:root.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --muted-foreground: #a1a1aa; /* zinc-400 for dark theme */
  --muted-foreground-light: #71717a; /* zinc-500 for dark theme */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted-foreground-light: var(--muted-foreground-light);
  --font-sans: var(--font-inter);
  --font-heading: var(--font-poppins);
}

body {
  font-family: var(--font-inter), ui-sans-serif, system-ui, -apple-system,
    "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  background: var(--background);
  color: var(--foreground);
  transition: background-color 0.3s ease, color 0.3s ease;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-poppins), ui-sans-serif, system-ui, -apple-system,
    "Segoe UI", Roboto, "Helvetica Neue", Arial;
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

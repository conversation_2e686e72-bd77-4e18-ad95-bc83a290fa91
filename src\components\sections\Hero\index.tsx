import React from "react";
import Container from "../../Container";

export default function Hero() {
  return (
    <section className="py-16 sm:py-24 bg-background">
      <Container className="text-center">
        <div className="inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium gap-2">
          <span className="inline-block h-2 w-2 rounded-full bg-emerald-500" />
          New template • Next.js + Tailwind
        </div>
        <h1 className="mt-6 text-4xl sm:text-5xl md:text-6xl font-heading font-semibold tracking-tight">
          Jadikan brand Anda menonjol dengan landing page modern
        </h1>
        <p className="mt-4 text-base sm:text-lg text-muted-foreground max-w-2xl mx-auto">
          Template responsif, cepat, dan mudah dikustomisasi. Cocok untuk bisnis, produk, atau kampanye.
        </p>
        <div className="mt-8 flex items-center justify-center gap-3">
          <a className="inline-flex h-11 items-center justify-center rounded-md bg-black text-white px-5 text-sm font-medium hover:opacity-90">
            Mulai Sekarang
          </a>
          <a className="inline-flex h-11 items-center justify-center rounded-md border px-5 text-sm font-medium hover:bg-zinc-50 dark:hover:bg-zinc-900">
            Lihat Demo
          </a>
        </div>
      </Container>
    </section>
  );
}


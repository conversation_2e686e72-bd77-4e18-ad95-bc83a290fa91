import React from "react";
import Container from "../../Container";

const faqs = [
  { q: "Apakah bisa di-deploy ke Vercel?", a: "Ya, Next.js sangat cocok untuk Vercel." },
  { q: "Apakah mudah diubah?", a: "Semua komponen dibuat modular dan mudah dikustomisasi." },
  { q: "Apakah SEO-friendly?", a: "Iya, dengan meta tag dan praktik Next.js modern." },
];

export default function FAQ() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">FAQ</h2>
        <div className="mt-6 grid gap-4">
          {faqs.map((f) => (
            <details key={f.q} className="rounded-lg border p-4">
              <summary className="cursor-pointer font-medium">{f.q}</summary>
              <p className="mt-2 text-sm text-muted-foreground">{f.a}</p>
            </details>
          ))}
        </div>
      </Container>
    </section>
  );
}


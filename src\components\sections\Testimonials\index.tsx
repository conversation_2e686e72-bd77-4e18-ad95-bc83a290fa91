import React from "react";
import Container from "../../Container";

const testimonials = [
  { name: "<PERSON><PERSON>", role: "<PERSON><PERSON><PERSON><PERSON>", quote: "Template ini bikin landing page saya siap jual dalam hitungan jam." },
  { name: "<PERSON><PERSON>", role: "Marketing", quote: "Desain clean dan mudah diubah sesuai brand." },
  { name: "<PERSON><PERSON>", role: "Founder", quote: "Performa cepat dan SEO oke. Recommended!" },
];

export default function Testimonials() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Testimoni</h2>
        <div className="mt-6 grid gap-6 sm:grid-cols-3">
          {testimonials.map((t) => (
            <figure key={t.name} className="rounded-xl border p-6 bg-white/50 dark:bg-zinc-900/50">
              <blockquote className="text-sm text-foreground">“{t.quote}”</blockquote>
              <figcaption className="mt-4 text-sm font-medium">
                {t.name} · <span className="text-muted-foreground-light">{t.role}</span>
              </figcaption>
            </figure>
          ))}
        </div>
      </Container>
    </section>
  );
}

